#pragma once

#include "../../frame/strategy_logger.h"
#include "fast_trader_elite/data_model/field.h"
#include <atomic>
#include <cpp_frame/shm/shm_util.h>
#include <limits>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace fast_trader_elite::strategy {

// 魔数，用于验证共享内存格式
constexpr uint32_t KLINE_SHM_MAGIC = 0x4B4C494E; // "KLIN" in ASCII
constexpr uint32_t KLINE_SHM_VERSION = 1;
constexpr uint32_t MAX_CONTRACTS = 1000;           // 最大合约数量

/**
 * @brief K线数据缓存类
 *
 * 用于缓存K线数据，包括时间戳、合约名、开盘价、最高价、最低价、收盘价和成交量
 */
class kline_cache {
public:
  kline_cache() = default;
  ~kline_cache() = default;

  // 清空缓存
  void clear() {
    timestamps_.clear();
    open_prices_.clear();
    high_prices_.clear();
    low_prices_.clear();
    close_prices_.clear();
    volumes_.clear();
  }

  // 添加一条K线数据
  void add(uint64_t timestamp, const std::string &instrument, double open,
           double high, double low, double close, double volume) {
    timestamps_.push_back(timestamp);
    open_prices_.push_back(open);
    high_prices_.push_back(high);
    low_prices_.push_back(low);
    close_prices_.push_back(close);
    volumes_.push_back(volume);
  }

  // 获取缓存大小
  size_t size() const { return timestamps_.size(); }

  // 数据成员
  std::vector<uint64_t> timestamps_;
  std::vector<double> open_prices_;
  std::vector<double> high_prices_;
  std::vector<double> low_prices_;
  std::vector<double> close_prices_;
  std::vector<double> volumes_;
};

// 共享内存头部结构
struct kline_shm_header {
  uint32_t magic;                   // 魔数，用于验证共享内存格式
  uint32_t version;                 // 版本号
  uint32_t max_contracts;           // 最大合约数量
  uint32_t max_klines_per_contract; // 每个合约的最大K线数量
  uint32_t contract_count;          // 当前合约数量
  uint64_t last_kline_close_time; // 最后更新时间 这一轮K线闭合更新 K线收盘时间
  uint64_t last_update_timestamp; // 最后更新时间戳
  uint32_t reserved[6];           // 保留字段，用于未来扩展
};

// 合约元数据结构
struct contract_metadata {
  char instrument_name[64]; // 合约名称
  alignas(4)
      std::atomic<uint32_t> kline_count; // 当前K线数量，使用原子操作保证可见性
  alignas(4) std::atomic<
      uint32_t> head_index; // 环形缓冲区头部索引，使用原子操作保证可见性
  uint64_t last_timestamp; // 最后更新时间戳 合约K线收盘时间
  uint32_t reserved[2]; // 保留字段（减少为2个以保持结构大小不变）
};

// K线数据结构
struct kline_data {
  uint64_t timestamp; // 时间戳
  double open;        // 开盘价
  double high;        // 最高价
  double low;         // 最低价
  double close;       // 收盘价
  double volume;      // 成交量
};

// 共享内存总体结构
struct kline_shm {
  kline_shm_header header;                    // 头部信息
  contract_metadata contracts[MAX_CONTRACTS]; // 合约元数据数组
  kline_data data[];                          // K线数据柔性数组 [MAX_CONTRACTS * max_klines_per_contract]
};

/**
 * @brief 共享内存K线数据管理器
 *
 * 将所有K线数据存储在共享内存中，支持多个合约的数据存储
 */
class shm_kline_manager {
public:
  shm_kline_manager(const std::string &save_dir,
                    fast_trader_elite::strategy::logger &logger,
                    const std::string &filename = "kline_data.shm",
                    uint32_t max_klines_per_contract = 13000);
  ~shm_kline_manager();

  bool init();
  bool init(const std::vector<std::string> &instruments);
  void add_kline_data(const kline_market_data_field *kline);
  bool flush_to_shm();
  bool flush_to_shm(const std::string &ins);
  void set_max_cache_size(size_t size) { max_cache_size_ = size; }
  uint64_t get_latest_timestamp(const std::string &instrument);

  // 设置最后更新时间，用于读端检测数据更新
  void set_last_update_time(uint64_t timestamp, bool force = false);

private:
  bool create_shm();
  void write_data_to_shm(const std::string &instrument);
  size_t calculate_shm_size() const;
  kline_data* get_kline_data_ptr(uint32_t contract_index, uint32_t kline_index);

  std::string save_dir_;
  std::string shm_path_;
  fast_trader_elite::strategy::logger &logger_;

  kline_shm *shm_ptr_;
  std::unordered_map<std::string, kline_cache> kline_caches_;
  std::unordered_map<std::string, int> contract_indices_;
  std::unordered_map<std::string, uint64_t> last_timestamps_; // 每个合约的最后时间戳

  uint32_t max_klines_per_contract_; // 每个合约的最大K线数量（可配置）
  size_t max_cache_size_{4000}; // 最大缓存大小
  bool initialized_{false};     // 是否已初始化
  std::mutex mutex_; // 用于保护共享内存访问的互斥锁（仅用于合约创建和初始化）
};

} // namespace fast_trader_elite::strategy
