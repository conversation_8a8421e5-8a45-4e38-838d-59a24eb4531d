# K线数据完整性保障系统 - 进程调度设计文档

## 1. 系统概述

### 1.1 架构组件
- **recorder** (kline_feed_shm_v1): 数据落地程序，负责接收WebSocket数据并写入共享内存
- **monitor_fixer** (fix_md_tools): 数据监控和补全程序，检测缺失数据并通过API补全
- **reader**: 数据读取程序，从共享内存读取K线数据进行业务处理
- **python_scheduler**: Python进程调度器，负责进程生命周期管理和协调

### 1.2 设计原则
- **一旦检测到缺失，必须重启recorder**：确保数据完整性，避免部分修复的复杂性
- **进程隔离**：各组件独立运行，通过共享内存通信
- **自动恢复**：系统具备自动检测和恢复能力
- **状态透明**：所有组件状态可监控和追踪

## 2. 共享内存结构设计

### 2.1 数据共享内存 (kline_shm)
```
路径: /home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm
结构: kline_shm_header + contract_metadata[1000] + kline_data[]
功能: 存储K线数据，支持最大1000个合约
```

**关键字段:**
- `last_kline_close_time`: 最后K线收盘时间，用于检测数据更新
- `last_update_timestamp`: 最后更新时间戳
- `contract_metadata.last_timestamp`: 每个合约的最后更新时间

### 2.2 进程控制共享内存 (process_control_shm)
```
路径: /home/<USER>/git/data/process_control.shm
功能: 进程间通信和状态协调
```

**核心字段:**
```cpp
struct process_control_shm {
    uint32_t magic;                           // 魔数 0x50435348 "PCSH"
    uint32_t version;                         // 版本号
    
    // 系统状态
    std::atomic<uint8_t> system_status;      // 0=正常, 1=检测中, 2=补全中, 3=异常
    std::atomic<uint64_t> status_update_time;
    
    // 进程控制
    std::atomic<uint8_t> should_kill_recorder; // 1=应该杀死recorder
    std::atomic<uint8_t> recorder_killed;      // 1=recorder已被杀死
    std::atomic<uint8_t> backfill_completed;   // 1=补全完成
    std::atomic<uint8_t> can_restart_recorder; // 1=可以重启recorder
    
    // 进程状态
    std::atomic<uint32_t> recorder_pid;        // recorder进程PID
    std::atomic<uint64_t> recorder_heartbeat;  // recorder心跳时间
    std::atomic<uint32_t> monitor_pid;         // monitor进程PID
    std::atomic<uint64_t> monitor_heartbeat;   // monitor心跳时间
    
    // 补全信息
    std::atomic<uint32_t> missing_contracts_count;
    std::atomic<uint64_t> backfill_start_time;
    std::atomic<uint64_t> backfill_end_time;
    char missing_contracts[1024];             // JSON格式缺失合约列表
    char control_message[256];                // 控制消息
    
    // 统计信息
    std::atomic<uint64_t> total_restarts;     // 总重启次数
    std::atomic<uint64_t> total_backfills;    // 总补全次数
};
```

## 3. 详细流程设计

### 3.1 正常运行状态

```
[recorder] → 接收WebSocket → 写入kline_shm → 更新心跳到control_shm
    ↓
[monitor_fixer] → 定期检查kline_shm → 检测数据完整性 → 更新检测状态
    ↓
[reader] → 读取kline_shm → 检查control_shm状态 → 正常处理数据
    ↓
[python_scheduler] → 监控control_shm → 检查进程心跳 → 记录运行状态
```

**检查频率:**
- monitor_fixer: 每10秒检查一次数据完整性
- python_scheduler: 每5秒检查一次进程状态
- recorder: 每秒更新一次心跳

### 3.2 缺失检测阶段

**monitor_fixer检测逻辑:**
```cpp
void check_data_integrity() {
    uint64_t current_time = get_current_time();
    
    // 检查全局更新时间
    uint64_t last_update = shm_ptr_->header.last_update_timestamp;
    if (current_time - last_update > 64 * 1000) {  // 64秒阈值
        detect_missing_data();
    }
    
    // 检查各合约更新时间
    for (uint32_t i = 0; i < shm_ptr_->header.contract_count; i++) {
        uint64_t contract_last = shm_ptr_->contracts[i].last_timestamp;
        if (current_time - contract_last > 64 * 1000) {
            add_missing_contract(shm_ptr_->contracts[i].instrument_name);
        }
    }
}
```

**状态更新:**
1. 设置 `system_status = 1` (检测中)
2. 记录缺失合约信息到 `missing_contracts`
3. 设置 `missing_contracts_count`
4. 更新 `status_update_time`

### 3.3 进程终止阶段

**python_scheduler响应逻辑:**
```python
def handle_missing_data_detected():
    # 1. 设置杀死标志
    control_shm.should_kill_recorder.store(1)
    control_shm.set_control_message("Killing recorder due to missing data")
    
    # 2. 等待recorder自行退出（优雅关闭，10秒超时）
    if not wait_for_recorder_exit(timeout=10):
        # 3. 强制杀死recorder
        force_kill_recorder()
    
    # 4. 确认recorder已终止
    control_shm.recorder_killed.store(1)
    control_shm.recorder_pid.store(0)
    
    # 5. 等待monitor_fixer开始补全
    wait_for_backfill_start()
```

**recorder退出逻辑:**
```cpp
// 在recorder的主循环中检查
void check_control_signals() {
    if (control_shm_->should_kill_recorder.load()) {
        STRA_LOG(logger_, STRA_WARN, "Received kill signal, exiting gracefully");
        
        // 停止数据写入
        stop_data_writing();
        
        // 清理资源
        cleanup_resources();
        
        // 退出程序
        exit(0);
    }
}
```

### 3.4 数据补全阶段

**monitor_fixer补全流程:**
```cpp
void start_backfill_process() {
    // 1. 等待recorder终止确认
    while (!control_shm_->recorder_killed.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 2. 设置补全状态
    control_shm_->system_status.store(2);  // 补全中
    control_shm_->backfill_start_time.store(get_current_time());
    
    // 3. 读取原始数据
    load_original_data_from_shm();
    
    // 4. 检测缺失范围
    detect_missing_ranges();
    
    // 5. 通过API补全数据
    for (auto& missing_range : missing_ranges) {
        fetch_and_fill_data(missing_range);
    }
    
    // 6. 写入修复后的数据到输出共享内存
    write_fixed_data_to_output_shm();
    
    // 7. 替换原始共享内存
    replace_original_shm_with_fixed_data();
    
    // 8. 标记补全完成
    control_shm_->backfill_completed.store(1);
    control_shm_->backfill_end_time.store(get_current_time());
    control_shm_->system_status.store(0);  // 恢复正常
}
```

**API补全细节:**
- 使用现有的HTTP API机制 (`ctx_->get_kline_by_range()`)
- 按时间范围请求缺失数据
- 验证返回数据的完整性和连续性
- 合并到原始数据中并去重

### 3.5 进程重启阶段

**python_scheduler重启逻辑:**
```python
def restart_recorder():
    # 1. 等待补全完成
    while not control_shm.backfill_completed.load():
        time.sleep(1)
        check_backfill_timeout()  # 检查补全超时
    
    # 2. 验证数据完整性
    if not verify_data_integrity():
        handle_backfill_failure()
        return
    
    # 3. 重置控制标志
    control_shm.should_kill_recorder.store(0)
    control_shm.recorder_killed.store(0)
    control_shm.backfill_completed.store(0)
    control_shm.can_restart_recorder.store(1)
    
    # 4. 启动新的recorder进程
    new_pid = start_recorder_process()
    control_shm.recorder_pid.store(new_pid)
    
    # 5. 验证新进程正常运行
    if verify_recorder_startup():
        control_shm.total_restarts.fetch_add(1)
        log_restart_success()
    else:
        handle_restart_failure()
```

**recorder启动检查:**
- 验证共享内存连接
- 确认WebSocket连接正常
- 检查数据写入功能
- 开始正常的心跳更新

## 4. Reader端适配策略

### 4.1 安全读取机制
```cpp
class SafeKlineReader {
public:
    bool is_data_safe_to_read() {
        uint8_t status = control_shm_->system_status.load();
        return status == 0;  // 只有正常状态才读取
    }
    
    std::vector<KlineData> read_kline_data(const std::string& instrument) {
        if (!is_data_safe_to_read()) {
            STRA_LOG(logger_, STRA_WARN, "Data not safe, using cached data");
            return get_cached_data(instrument);
        }
        
        return read_from_shm(instrument);
    }
};
```

### 4.2 降级处理策略
- **检测中**: 继续使用最后有效数据，记录警告
- **补全中**: 暂停新的业务处理，使用缓存数据
- **异常状态**: 进入安全模式，停止关键操作

## 5. Python调度器实现

### 5.1 主要功能模块
```python
class ProcessScheduler:
    def __init__(self):
        self.control_shm = ProcessControlSHM()
        self.processes = {}
        self.running = True
    
    def main_loop(self):
        while self.running:
            self.check_system_status()      # 检查系统状态变化
            self.check_process_heartbeats() # 检查进程心跳
            self.handle_control_actions()   # 处理控制动作
            self.update_statistics()        # 更新统计信息
            time.sleep(1)
    
    def check_system_status(self):
        status = self.control_shm.system_status.load()
        if status == 1:  # 检测到缺失
            self.handle_missing_data_detected()
        elif status == 2:  # 补全中
            self.monitor_backfill_progress()
    
    def check_process_heartbeats(self):
        current_time = time.time() * 1000
        
        # 检查recorder心跳
        recorder_heartbeat = self.control_shm.recorder_heartbeat.load()
        if current_time - recorder_heartbeat > 30000:  # 30秒超时
            self.handle_recorder_timeout()
        
        # 检查monitor心跳
        monitor_heartbeat = self.control_shm.monitor_heartbeat.load()
        if current_time - monitor_heartbeat > 60000:  # 60秒超时
            self.handle_monitor_timeout()
```

### 5.2 进程管理
```python
def start_recorder_process(self):
    cmd = [
        "/home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite",
        "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json"
    ]
    
    process = subprocess.Popen(cmd, cwd="/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build")
    self.processes['recorder'] = process
    return process.pid

def start_monitor_process(self):
    cmd = [
        "/home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite",
        "/home/<USER>/git/cross_selction_strategy/fix_md_tools/config/fix_md_tool_config.json"
    ]
    
    process = subprocess.Popen(cmd, cwd="/home/<USER>/git/cross_selction_strategy/fix_md_tools/build")
    self.processes['monitor'] = process
    return process.pid
```

### 5.3 异常处理
```python
def handle_backfill_timeout(self):
    """处理补全超时（15分钟无进展）"""
    self.kill_process('monitor')
    time.sleep(2)
    self.start_monitor_process()
    self.send_alert("Backfill timeout, restarted monitor process")

def handle_restart_failure(self):
    """处理重启失败"""
    self.restart_attempts += 1
    if self.restart_attempts >= 3:
        self.enter_maintenance_mode()
        self.send_critical_alert("Multiple restart failures, entering maintenance mode")
    else:
        time.sleep(5)  # 等待5秒后重试
        self.restart_recorder()
```

## 6. 配置和部署

### 6.1 目录结构
```
/home/<USER>/git/
├── cross_selction_strategy/
│   ├── kline_feed_shm_v1/          # recorder程序
│   ├── fix_md_tools/               # monitor_fixer程序
│   └── process_scheduler/          # Python调度器
├── data/
│   ├── kline_data.shm             # 主数据共享内存
│   ├── kline_data_fixed.shm       # 修复后数据共享内存
│   └── process_control.shm        # 进程控制共享内存
└── logs/
    ├── recorder.log
    ├── monitor.log
    └── scheduler.log
```

### 6.2 启动顺序
1. 启动Python调度器
2. 调度器启动monitor_fixer进程
3. 调度器启动recorder进程
4. 各进程开始正常运行和心跳更新

### 6.3 监控指标
- 系统状态变化次数
- 进程重启次数和频率
- 数据补全次数和耗时
- API调用成功率
- 心跳超时次数

## 7. 优势和特点

1. **数据完整性保障**: 一旦检测到缺失必须重启，确保数据100%准确
2. **自动化程度高**: 无需人工干预，系统自动检测、补全、重启
3. **状态透明**: 所有操作状态可追踪和监控
4. **容错能力强**: 多层异常处理和恢复机制
5. **扩展性好**: 易于添加新的监控指标和处理策略
6. **运维友好**: Python调度器便于配置和维护
