# K线数据完整性保障系统 - 进程调度设计文档

## 1. 系统概述

### 1.1 架构组件
- **recorder** (kline_feed_shm_v1): 数据落地程序，负责接收WebSocket数据并写入共享内存
- **monitor_fixer** (fix_md_tools): 数据监控和补全程序，检测缺失数据并通过API补全
- **reader**: 数据读取程序，从共享内存读取K线数据进行业务处理
- **python_scheduler**: Python进程调度器，负责进程生命周期管理和协调

### 1.2 设计原则
- **一旦检测到缺失，必须重启recorder**：确保数据完整性，避免部分修复的复杂性
- **进程隔离**：各组件独立运行，通过共享内存通信
- **自动恢复**：系统具备自动检测和恢复能力
- **状态透明**：所有组件状态可监控和追踪

## 2. 共享内存结构设计

### 2.1 数据共享内存 (kline_shm)
```
路径: /home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm
结构: kline_shm_header + contract_metadata[1000] + kline_data[]
功能: 存储K线数据，支持最大1000个合约
```

**关键字段:**
- `last_kline_close_time`: 最后K线收盘时间，用于检测数据更新
- `last_update_timestamp`: 最后更新时间戳
- `contract_metadata.last_timestamp`: 每个合约的最后更新时间

### 2.2 进程控制共享内存 (process_control_shm)
```
路径: /home/<USER>/git/data/process_control.shm
功能: 进程间通信和状态协调
```

**核心字段:**
```cpp
struct process_control_shm {
    uint32_t magic;                           // 魔数 0x50435348 "PCSH"
    uint32_t version;                         // 版本号
    
    // 系统状态
    std::atomic<uint8_t> system_status;      // 0=正常, 1=检测中, 2=补全中, 3=异常
    std::atomic<uint64_t> status_update_time;
    
    // 进程控制
    std::atomic<uint8_t> should_kill_recorder; // 1=应该杀死recorder
    std::atomic<uint8_t> recorder_killed;      // 1=recorder已被杀死
    std::atomic<uint8_t> backfill_completed;   // 1=补全完成
    std::atomic<uint8_t> can_restart_recorder; // 1=可以重启recorder
    
    // 进程状态
    std::atomic<uint32_t> recorder_pid;        // recorder进程PID
    std::atomic<uint64_t> recorder_heartbeat;  // recorder心跳时间
    std::atomic<uint32_t> monitor_pid;         // monitor进程PID
    std::atomic<uint64_t> monitor_heartbeat;   // monitor心跳时间
    
    // 补全信息
    std::atomic<uint32_t> missing_contracts_count;
    std::atomic<uint64_t> backfill_start_time;
    std::atomic<uint64_t> backfill_end_time;
    char missing_contracts[1024];             // JSON格式缺失合约列表
    char control_message[256];                // 控制消息
    
    // 统计信息
    std::atomic<uint64_t> total_restarts;     // 总重启次数
    std::atomic<uint64_t> total_backfills;    // 总补全次数
};
```

## 3. system_status状态维护机制

### 3.1 状态定义和转换
```cpp
enum class SystemStatus : uint8_t {
    NORMAL = 0,           // 正常运行
    DETECTING = 1,        // 检测数据缺失中
    BACKFILLING = 2,      // 数据补全中
    RESTARTING = 3,       // 进程重启中
    ERROR = 4,            // 系统异常
    MAINTENANCE = 5       // 维护模式
};
```

### 3.2 状态维护责任分工

**monitor_fixer负责:**
- `NORMAL → DETECTING`: 检测到数据缺失时
- `DETECTING → BACKFILLING`: 开始数据补全时
- `BACKFILLING → NORMAL`: 补全完成且数据验证通过时
- `任何状态 → ERROR`: 补全失败或API异常时

**python_scheduler负责:**
- `DETECTING → RESTARTING`: 决定杀死recorder时
- `RESTARTING → NORMAL`: recorder重启成功时
- `ERROR → MAINTENANCE`: 连续失败超过阈值时
- `MAINTENANCE → NORMAL`: 人工干预恢复时

### 3.3 状态转换流程图
```
NORMAL ──(缺失检测)──> DETECTING ──(开始补全)──> BACKFILLING
  ↑                                                    ↓
  └──(重启成功)── RESTARTING ←──(杀死进程)──────────────┘
                     ↓
                   ERROR ──(连续失败)──> MAINTENANCE
```

## 4. 详细流程设计

### 4.1 正常运行状态

```
[recorder] → 接收WebSocket → 写入kline_shm → 更新心跳到control_shm
    ↓
[monitor_fixer] → 定期检查kline_shm → 检测数据完整性 → 维护system_status
    ↓
[reader] → 读取kline_shm → 检查control_shm状态 → 根据状态决定处理策略
    ↓
[python_scheduler] → 监控control_shm → 检查进程心跳 → 执行进程控制
```

**检查频率:**
- monitor_fixer: 每10秒检查一次数据完整性
- python_scheduler: 每5秒检查一次进程状态和system_status
- recorder: 每秒更新一次心跳
- reader: 每次读取前检查system_status

### 4.2 缺失检测阶段

**monitor_fixer检测逻辑:**
```cpp
void check_data_integrity() {
    uint64_t current_time = get_current_time();
    bool has_missing = false;

    // 检查全局更新时间
    uint64_t last_update = shm_ptr_->header.last_update_timestamp;
    if (current_time - last_update > 64 * 1000) {  // 64秒阈值
        has_missing = true;
    }

    // 检查各合约更新时间
    std::vector<std::string> missing_contracts;
    for (uint32_t i = 0; i < shm_ptr_->header.contract_count; i++) {
        uint64_t contract_last = shm_ptr_->contracts[i].last_timestamp;
        if (current_time - contract_last > 64 * 1000) {
            missing_contracts.push_back(shm_ptr_->contracts[i].instrument_name);
            has_missing = true;
        }
    }

    // 状态转换: NORMAL → DETECTING
    if (has_missing && control_shm_->system_status.load() == 0) {
        control_shm_->system_status.store(1);  // DETECTING
        update_missing_contracts_info(missing_contracts);
        control_shm_->status_update_time.store(current_time);

        STRA_LOG(logger_, STRA_WARN, "Data missing detected, status: NORMAL → DETECTING");
    }
}
```

**状态更新详情:**
1. 设置 `system_status = 1` (DETECTING)
2. 记录缺失合约信息到 `missing_contracts` (JSON格式)
3. 设置 `missing_contracts_count`
4. 更新 `status_update_time`
5. 记录检测开始时间

### 4.3 进程控制阶段

**python_scheduler响应逻辑:**
```python
def handle_detecting_status():
    """处理DETECTING状态，决定是否杀死recorder"""
    current_status = control_shm.system_status.load()
    if current_status != 1:  # 不是DETECTING状态
        return

    # 状态转换: DETECTING → RESTARTING
    control_shm.system_status.store(3)  # RESTARTING
    control_shm.set_control_message("Starting recorder restart process")

    # 通过PID管理进程，不依赖C++的exit()
    recorder_pid = control_shm.recorder_pid.load()
    if recorder_pid > 0:
        # 1. 优雅关闭（发送SIGTERM）
        try:
            os.kill(recorder_pid, signal.SIGTERM)
            if wait_for_process_exit(recorder_pid, timeout=10):
                logger.info(f"Recorder {recorder_pid} exited gracefully")
            else:
                # 2. 强制杀死（发送SIGKILL）
                os.kill(recorder_pid, signal.SIGKILL)
                logger.warning(f"Force killed recorder {recorder_pid}")
        except ProcessLookupError:
            logger.info(f"Recorder {recorder_pid} already exited")

    # 3. 清理进程状态
    control_shm.recorder_pid.store(0)
    control_shm.recorder_heartbeat.store(0)

    # 4. 等待monitor_fixer开始补全
    self.wait_for_backfill_start()

def wait_for_process_exit(pid, timeout=10):
    """等待进程退出"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            os.kill(pid, 0)  # 检查进程是否存在
            time.sleep(0.1)
        except ProcessLookupError:
            return True  # 进程已退出
    return False  # 超时
```

**recorder进程无需修改:**
- 不需要在C++代码中添加exit()逻辑
- 完全由python_scheduler通过PID管理
- 进程被杀死后自然退出，无需额外处理

### 4.4 智能数据补全阶段

**monitor_fixer智能补全流程:**
```cpp
void start_intelligent_backfill_process() {
    // 检查当前状态，只有在DETECTING状态才开始补全
    uint8_t current_status = control_shm_->system_status.load();
    if (current_status != 1) {  // 不是DETECTING状态
        return;
    }

    // 状态转换: DETECTING → BACKFILLING
    control_shm_->system_status.store(2);  // BACKFILLING
    control_shm_->backfill_start_time.store(get_current_time());
    control_shm_->set_control_message("Starting intelligent data backfill process");

    try {
        // 1. 读取原始数据从共享内存
        load_original_data_from_shm();

        // 2. 检测缺失范围（基于现有的data_integrity_checker）
        detect_missing_ranges();

        // 3. 智能补全：区分可补全和不可补全的合约
        std::vector<std::string> successfully_filled;
        std::vector<std::string> expired_contracts;
        std::vector<std::string> failed_contracts;

        for (const auto& [instrument, ranges] : missing_ranges_) {
            BackfillResult result = attempt_contract_backfill(instrument, ranges);

            switch (result.status) {
                case BackfillStatus::SUCCESS:
                    successfully_filled.push_back(instrument);
                    control_shm_->backfilled_contracts.fetch_add(1);
                    break;

                case BackfillStatus::CONTRACT_EXPIRED:
                    expired_contracts.push_back(instrument);
                    mark_contract_for_removal(instrument);
                    break;

                case BackfillStatus::API_FAILED:
                    failed_contracts.push_back(instrument);
                    break;
            }
        }

        // 4. 处理补全结果
        if (!successfully_filled.empty()) {
            // 写入修复后的数据到输出共享内存
            write_fixed_data_to_output_shm();

            // 原子性替换原始共享内存（重命名文件）
            replace_original_shm_with_fixed_data();
        }

        // 5. 通知recorder剔除过期合约
        if (!expired_contracts.empty()) {
            notify_recorder_remove_contracts(expired_contracts);
        }

        // 6. 决定最终状态
        if (failed_contracts.empty()) {
            // 所有合约都处理完成（成功补全或标记过期）
            control_shm_->system_status.store(0);  // NORMAL
            control_shm_->set_control_message("Data backfill completed successfully");
            STRA_LOG(logger_, STRA_INFO, "Intelligent backfill completed: {} filled, {} expired",
                     successfully_filled.size(), expired_contracts.size());
        } else {
            // 仍有失败的合约，但不阻塞系统运行
            control_shm_->system_status.store(0);  // NORMAL (允许系统继续运行)
            control_shm_->set_control_message("Partial backfill completed with some failures");
            STRA_LOG(logger_, STRA_WARN, "Partial backfill: {} filled, {} expired, {} failed",
                     successfully_filled.size(), expired_contracts.size(), failed_contracts.size());
        }

        control_shm_->backfill_end_time.store(get_current_time());
        control_shm_->total_backfills.fetch_add(1);

    } catch (const std::exception& e) {
        // 状态转换: BACKFILLING → NORMAL (不进入ERROR状态，允许系统继续运行)
        control_shm_->system_status.store(0);  // NORMAL
        control_shm_->set_control_message("Backfill encountered errors but system continues");

        STRA_LOG(logger_, STRA_ERROR, "Backfill failed: {}, but system continues running", e.what());
    }
}

BackfillResult attempt_contract_backfill(const std::string& instrument,
                                        const std::vector<TimeRange>& ranges) {
    try {
        std::vector<KlineData> all_filled_data;

        for (const auto& range : ranges) {
            auto klines = api_fetcher_.fetch_klines(instrument, range.start, range.end);

            if (klines.empty()) {
                // API返回空数据，检查是否是合约过期
                if (is_contract_expired(instrument)) {
                    return {BackfillStatus::CONTRACT_EXPIRED, {}};
                } else {
                    // 可能是临时网络问题，继续尝试其他范围
                    continue;
                }
            }

            all_filled_data.insert(all_filled_data.end(), klines.begin(), klines.end());
        }

        if (!all_filled_data.empty()) {
            // 合并到原始数据
            merge_backfilled_data(instrument, all_filled_data);
            return {BackfillStatus::SUCCESS, all_filled_data};
        } else {
            return {BackfillStatus::API_FAILED, {}};
        }

    } catch (const APIException& e) {
        if (e.is_contract_not_found() || e.is_contract_expired()) {
            return {BackfillStatus::CONTRACT_EXPIRED, {}};
        } else {
            return {BackfillStatus::API_FAILED, {}};
        }
    }
}
```

**API补全细节:**
- 使用现有的HTTP API机制 (`ctx_->get_kline_by_range()`)
- 按时间范围请求缺失数据
- 验证返回数据的完整性和连续性
- 合并到原始数据中并去重

### 4.5 进程重启阶段

**python_scheduler重启逻辑:**
```python
def monitor_system_status(self):
    """监控系统状态变化并处理"""
    current_status = self.control_shm.system_status.load()

    if current_status == 0 and self.previous_status == 2:  # BACKFILLING → NORMAL
        self.handle_backfill_completed()
    elif current_status == 4:  # ERROR状态
        self.handle_error_status()

def handle_backfill_completed(self):
    """处理补全完成，重启recorder"""
    logger.info("Backfill completed, restarting recorder")

    # 1. 验证数据完整性（可选，monitor_fixer已验证）
    if not self.verify_data_integrity():
        self.handle_verification_failure()
        return

    # 2. 启动新的recorder进程
    try:
        new_pid = self.start_recorder_process()
        self.control_shm.recorder_pid.store(new_pid)

        # 3. 验证新进程正常启动（检查心跳）
        if self.verify_recorder_startup(new_pid, timeout=30):
            self.control_shm.total_restarts.fetch_add(1)
            logger.info(f"Recorder restarted successfully, PID: {new_pid}")
        else:
            raise Exception("Recorder startup verification failed")

    except Exception as e:
        logger.error(f"Failed to restart recorder: {e}")
        self.control_shm.system_status.store(4)  # ERROR
        self.handle_restart_failure()

def verify_recorder_startup(self, pid, timeout=30):
    """验证recorder启动成功"""
    start_time = time.time()

    while time.time() - start_time < timeout:
        # 检查进程是否存在
        try:
            os.kill(pid, 0)
        except ProcessLookupError:
            logger.error(f"Recorder process {pid} died during startup")
            return False

        # 检查心跳更新
        heartbeat = self.control_shm.recorder_heartbeat.load()
        if heartbeat > 0:
            logger.info(f"Recorder {pid} heartbeat detected, startup successful")
            return True

        time.sleep(1)

    logger.error(f"Recorder {pid} startup timeout")
    return False
```

**recorder启动检查清单:**
- 进程存活检查（通过PID）
- 心跳信号检查（30秒内必须有心跳）
- 共享内存连接验证（通过心跳间接验证）
- WebSocket连接状态（通过数据写入间接验证）

## 5. Reader端优化设计

### 5.1 核心问题重新分析
**问题1: 频繁状态检查影响性能**
- Reader每分钟读取，频繁检查system_status会降低性能
- 应该基于数据的update_time而不是系统状态来决定读取策略

**问题2: 合约过期和剔除机制**
- HTTP API无法获取过期合约数据
- recorder会自动剔除长时间无数据的合约
- monitor补全时也会遇到相同问题

**问题3: 补全期间的数据连续性**
- 补全可能需要较长时间（几分钟到十几分钟）
- Reader不应该等待补全完成，应该继续使用现有数据

### 5.2 重新设计的Reader策略

```cpp
class OptimizedKlineReader {
private:
    uint64_t last_global_update_time_ = 0;
    std::unordered_map<std::string, uint64_t> last_contract_update_times_;
    std::unordered_map<std::string, std::vector<KlineData>> cached_data_;
    uint64_t last_status_check_time_ = 0;
    constexpr uint64_t STATUS_CHECK_INTERVAL = 60 * 1000;  // 1分钟检查一次状态

public:
    std::vector<KlineData> read_kline_data(const std::string& instrument) {
        uint64_t current_time = get_current_time();

        // 1. 轻量级检查：只检查数据更新时间
        uint64_t global_update = shm_ptr_->header.last_update_timestamp;
        uint32_t contract_idx = find_contract_index(instrument);

        if (contract_idx == UINT32_MAX) {
            // 合约不存在（可能被剔除），返回缓存数据
            return get_cached_data(instrument);
        }

        uint64_t contract_update = shm_ptr_->contracts[contract_idx].last_timestamp;

        // 2. 判断是否需要读取新数据
        bool need_read = false;
        if (global_update > last_global_update_time_) {
            need_read = true;
            last_global_update_time_ = global_update;
        }

        if (contract_update > last_contract_update_times_[instrument]) {
            need_read = true;
            last_contract_update_times_[instrument] = contract_update;
        }

        // 3. 只在需要时读取数据
        if (need_read) {
            auto new_data = read_contract_data_from_shm(instrument);
            if (!new_data.empty()) {
                cached_data_[instrument] = new_data;
                return new_data;
            }
        }

        // 4. 定期检查系统状态（降低频率）
        if (current_time - last_status_check_time_ > STATUS_CHECK_INTERVAL) {
            check_system_status_periodically();
            last_status_check_time_ = current_time;
        }

        // 5. 返回缓存数据
        return cached_data_[instrument];
    }

private:
    void check_system_status_periodically() {
        uint8_t status = control_shm_->system_status.load();

        switch (status) {
            case 0:  // NORMAL - 检查是否需要重新映射
                check_and_remap_if_needed();
                break;

            case 2:  // BACKFILLING - 记录日志但继续使用缓存
                STRA_LOG(logger_, STRA_INFO, "System in backfill mode, using cached data");
                break;

            case 4:  // ERROR - 记录错误但继续运行
                STRA_LOG(logger_, STRA_WARN, "System in error state, using cached data");
                break;

            case 5:  // MAINTENANCE - 可能需要特殊处理
                STRA_LOG(logger_, STRA_WARN, "System in maintenance mode");
                break;
        }
    }

    void check_and_remap_if_needed() {
        // 检查共享内存文件是否被替换（通过文件修改时间或inode）
        struct stat file_stat;
        if (stat(shm_path_.c_str(), &file_stat) == 0) {
            if (file_stat.st_mtime > last_shm_mtime_) {
                STRA_LOG(logger_, STRA_INFO, "Shared memory file updated, remapping");
                remap_shared_memory();
                last_shm_mtime_ = file_stat.st_mtime;

                // 重新读取所有数据
                reload_all_cached_data();
            }
        }
    }
};
```

### 5.3 合约剔除和过期处理

```cpp
// 在monitor_fixer中处理合约过期
class SmartDataFixer {
public:
    void handle_missing_contracts() {
        for (const auto& missing_contract : missing_contracts_) {
            try {
                // 尝试通过API补全
                auto klines = fetch_missing_data(missing_contract);
                if (!klines.empty()) {
                    fill_missing_data(missing_contract, klines);
                } else {
                    // API返回空数据，可能是合约过期
                    handle_expired_contract(missing_contract);
                }
            } catch (const APIException& e) {
                if (e.is_contract_expired()) {
                    handle_expired_contract(missing_contract);
                } else {
                    // 其他API错误，记录并跳过
                    STRA_LOG(logger_, STRA_ERROR, "API error for {}: {}",
                             missing_contract, e.what());
                }
            }
        }
    }

private:
    void handle_expired_contract(const std::string& instrument) {
        // 1. 标记合约为过期状态
        mark_contract_expired(instrument);

        // 2. 通知recorder剔除该合约（通过控制共享内存）
        add_to_removal_list(instrument);

        // 3. 从补全列表中移除
        remove_from_missing_list(instrument);

        STRA_LOG(logger_, STRA_WARN, "Contract {} marked as expired, will be removed",
                 instrument);
    }
};
```

### 5.4 优化后的处理策略

**Reader端策略:**
1. **基于update_time读取**: 只有数据更新时才读取，避免无效读取
2. **降低状态检查频率**: 从每次读取检查改为每分钟检查一次
3. **智能缓存管理**: 维护本地缓存，合约不存在时返回缓存数据
4. **文件变化检测**: 通过文件修改时间检测共享内存是否被替换

**补全策略:**
1. **过期合约处理**: API无法获取时标记为过期，通知recorder剔除
2. **部分补全**: 能补全的补全，不能补全的标记处理
3. **非阻塞补全**: 补全过程不影响Reader正常读取

**性能优化:**
1. **减少系统调用**: 降低状态检查频率
2. **智能读取**: 基于时间戳判断是否需要读取
3. **缓存策略**: 合理使用缓存减少共享内存访问

## 6. Python调度器完整实现

### 6.1 主要功能模块
```python
class ProcessScheduler:
    def __init__(self):
        self.control_shm = ProcessControlSHM()
        self.processes = {}
        self.running = True
        self.previous_status = 0
        self.restart_attempts = 0
        self.max_restart_attempts = 3

    def main_loop(self):
        """主循环：监控状态变化和进程健康"""
        while self.running:
            try:
                self.monitor_system_status()    # 监控系统状态变化
                self.check_process_heartbeats() # 检查进程心跳
                self.handle_status_transitions() # 处理状态转换
                self.update_statistics()        # 更新统计信息
                self.cleanup_dead_processes()   # 清理死进程
                time.sleep(1)
            except Exception as e:
                logger.error(f"Main loop error: {e}")
                time.sleep(5)

    def handle_status_transitions(self):
        """处理状态转换"""
        current_status = self.control_shm.system_status.load()

        # DETECTING状态：杀死recorder
        if current_status == 1 and self.previous_status == 0:
            self.handle_detecting_status()

        # NORMAL状态且从BACKFILLING恢复：重启recorder
        elif current_status == 0 and self.previous_status == 2:
            self.handle_backfill_completed()

        # ERROR状态：处理错误
        elif current_status == 4:
            self.handle_error_status()

        self.previous_status = current_status

    def check_process_heartbeats(self):
        """检查进程心跳"""
        current_time = int(time.time() * 1000)

        # 检查recorder心跳（仅在应该运行时）
        recorder_pid = self.control_shm.recorder_pid.load()
        if recorder_pid > 0:
            recorder_heartbeat = self.control_shm.recorder_heartbeat.load()
            if current_time - recorder_heartbeat > 30000:  # 30秒超时
                self.handle_recorder_timeout(recorder_pid)

        # 检查monitor心跳
        monitor_pid = self.control_shm.monitor_pid.load()
        if monitor_pid > 0:
            monitor_heartbeat = self.control_shm.monitor_heartbeat.load()
            if current_time - monitor_heartbeat > 60000:  # 60秒超时
                self.handle_monitor_timeout(monitor_pid)
```

### 6.2 进程管理
```python
def start_recorder_process(self):
    """启动recorder进程"""
    cmd = [
        "/home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite",
        "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json"
    ]

    try:
        process = subprocess.Popen(
            cmd,
            cwd="/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/build",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        self.processes['recorder'] = process
        logger.info(f"Started recorder process, PID: {process.pid}")
        return process.pid
    except Exception as e:
        logger.error(f"Failed to start recorder: {e}")
        raise

def start_monitor_process(self):
    """启动monitor进程"""
    # 修改配置文件，设置auto_fix=true
    config_path = "/home/<USER>/git/cross_selction_strategy/fix_md_tools/config/fix_md_tool_config.json"
    self.update_monitor_config(config_path, auto_fix=True)

    cmd = [
        "/home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite",
        config_path
    ]

    try:
        process = subprocess.Popen(
            cmd,
            cwd="/home/<USER>/git/cross_selction_strategy/fix_md_tools/build",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        self.processes['monitor'] = process
        logger.info(f"Started monitor process, PID: {process.pid}")
        return process.pid
    except Exception as e:
        logger.error(f"Failed to start monitor: {e}")
        raise

def update_monitor_config(self, config_path, auto_fix=True):
    """更新monitor配置文件"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        config['auto_fix'] = auto_fix

        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)

        logger.info(f"Updated monitor config: auto_fix={auto_fix}")
    except Exception as e:
        logger.error(f"Failed to update monitor config: {e}")

def kill_process(self, process_name, pid=None):
    """杀死指定进程"""
    if pid is None:
        if process_name in self.processes:
            pid = self.processes[process_name].pid
        else:
            logger.warning(f"No PID found for {process_name}")
            return False

    try:
        # 优雅关闭
        os.kill(pid, signal.SIGTERM)
        if self.wait_for_process_exit(pid, timeout=10):
            logger.info(f"{process_name} {pid} exited gracefully")
        else:
            # 强制杀死
            os.kill(pid, signal.SIGKILL)
            logger.warning(f"Force killed {process_name} {pid}")

        # 清理进程记录
        if process_name in self.processes:
            del self.processes[process_name]

        return True
    except ProcessLookupError:
        logger.info(f"{process_name} {pid} already exited")
        return True
    except Exception as e:
        logger.error(f"Failed to kill {process_name} {pid}: {e}")
        return False
```

### 6.3 异常处理和超时管理
```python
def handle_error_status(self):
    """处理ERROR状态"""
    error_msg = self.control_shm.get_error_message()
    logger.error(f"System in ERROR state: {error_msg}")

    self.restart_attempts += 1
    if self.restart_attempts >= self.max_restart_attempts:
        self.enter_maintenance_mode()
        return

    # 尝试恢复：重启所有进程
    self.restart_all_processes()

def handle_backfill_timeout(self):
    """处理补全超时（15分钟无进展）"""
    logger.warning("Backfill timeout detected")

    # 杀死monitor进程并重启
    monitor_pid = self.control_shm.monitor_pid.load()
    if monitor_pid > 0:
        self.kill_process('monitor', monitor_pid)

    time.sleep(2)
    new_monitor_pid = self.start_monitor_process()
    self.control_shm.monitor_pid.store(new_monitor_pid)

    # 重置补全状态
    self.control_shm.system_status.store(1)  # 回到DETECTING状态
    self.send_alert("Backfill timeout, restarted monitor process")

def handle_recorder_timeout(self, pid):
    """处理recorder心跳超时"""
    logger.warning(f"Recorder {pid} heartbeat timeout")

    # 检查进程是否真的死了
    try:
        os.kill(pid, 0)
        # 进程存在但无心跳，强制重启
        self.kill_process('recorder', pid)
        self.restart_recorder_after_timeout()
    except ProcessLookupError:
        # 进程已死，直接重启
        self.control_shm.recorder_pid.store(0)
        self.restart_recorder_after_timeout()

def handle_monitor_timeout(self, pid):
    """处理monitor心跳超时"""
    logger.warning(f"Monitor {pid} heartbeat timeout")

    try:
        os.kill(pid, 0)
        self.kill_process('monitor', pid)
    except ProcessLookupError:
        pass

    # 重启monitor
    new_monitor_pid = self.start_monitor_process()
    self.control_shm.monitor_pid.store(new_monitor_pid)

def enter_maintenance_mode(self):
    """进入维护模式"""
    self.control_shm.system_status.store(5)  # MAINTENANCE
    self.control_shm.set_control_message("System in maintenance mode - manual intervention required")

    # 停止所有进程
    for process_name, process in self.processes.items():
        self.kill_process(process_name, process.pid)

    self.send_critical_alert("System entered maintenance mode")
    logger.critical("System entered maintenance mode")

def restart_all_processes(self):
    """重启所有进程"""
    logger.info("Restarting all processes")

    # 杀死所有现有进程
    for process_name in list(self.processes.keys()):
        process = self.processes[process_name]
        self.kill_process(process_name, process.pid)

    time.sleep(5)

    # 重启monitor
    monitor_pid = self.start_monitor_process()
    self.control_shm.monitor_pid.store(monitor_pid)

    # 重启recorder
    recorder_pid = self.start_recorder_process()
    self.control_shm.recorder_pid.store(recorder_pid)

    # 重置状态
    self.control_shm.system_status.store(0)  # NORMAL
    self.restart_attempts = 0
```

## 7. 关键问题解答总结

### 7.1 Reader性能优化策略
**问题**: Reader每分钟读取时频繁检查状态影响性能

**解决方案**:
- **基于update_time读取**: 只有数据更新时才读取，避免无效操作
- **降低状态检查频率**: 从每次读取改为每分钟检查一次
- **智能缓存管理**: 维护本地缓存，减少共享内存访问
- **文件变化检测**: 通过文件修改时间检测共享内存替换

### 7.2 合约过期和剔除处理
**问题**: HTTP API无法获取过期合约数据，recorder会剔除长时间无数据的合约

**解决方案**:
- **智能补全**: 区分可补全、过期、失败三种情况
- **过期合约处理**: API无法获取时标记为过期，通知recorder剔除
- **部分补全策略**: 能补全的补全，不能补全的标记处理，不阻塞系统
- **非阻塞设计**: 补全失败不影响系统正常运行

### 7.3 system_status维护机制
**责任分工明确:**
- **monitor_fixer**: 负责数据相关状态转换 (NORMAL↔DETECTING↔BACKFILLING)
- **python_scheduler**: 负责进程相关状态转换 (DETECTING→RESTARTING, ERROR→MAINTENANCE)

**状态转换优化:**
- 补全失败不进入ERROR状态，允许系统继续运行
- 使用atomic操作确保状态更新的原子性
- 状态转换有明确的触发条件和责任方

### 7.4 进程控制方式
**完全由Python管理:**
- 不依赖C++程序的exit()调用
- 通过PID进行进程生命周期管理
- 支持优雅关闭和强制杀死

**进程监控:**
- 心跳机制检测进程健康状态
- 超时自动重启机制
- 启动验证确保进程正常工作

## 8. 配置和部署

### 8.1 目录结构
```
/home/<USER>/git/
├── cross_selction_strategy/
│   ├── kline_feed_shm_v1/          # recorder程序
│   │   ├── build/libkline_feed_shm_v1.so
│   │   └── config/kline_feed_shm.json
│   ├── fix_md_tools/               # monitor_fixer程序
│   │   ├── build/libfix_md_tool.so
│   │   └── config/fix_md_tool_config.json
│   └── process_scheduler/          # Python调度器
│       ├── scheduler.py
│       ├── process_control_shm.py
│       └── config.json
├── fast_trader_elite_pkg/main/
│   └── fast_trader_elite           # 主执行程序
├── data/
│   ├── kline_data.shm             # 主数据共享内存
│   ├── kline_data_fixed.shm       # 修复后数据共享内存
│   └── process_control.shm        # 进程控制共享内存
└── logs/
    ├── recorder.log
    ├── monitor.log
    └── scheduler.log
```

### 8.2 启动顺序和依赖
1. **Python调度器启动**: 初始化进程控制共享内存
2. **启动monitor进程**: 设置auto_fix=true，开始数据监控
3. **启动recorder进程**: 开始数据收集和写入
4. **进入正常运行**: 所有进程开始心跳更新和数据处理

### 8.3 配置参数
```json
{
  "timeouts": {
    "recorder_heartbeat": 30,      // 秒
    "monitor_heartbeat": 60,       // 秒
    "backfill_timeout": 900,       // 15分钟
    "startup_verification": 30     // 秒
  },
  "thresholds": {
    "data_missing": 64,            // 秒
    "max_restart_attempts": 3
  },
  "paths": {
    "data_shm": "/home/<USER>/git/data/kline_data.shm",
    "control_shm": "/home/<USER>/git/data/process_control.shm",
    "recorder_config": "/home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json",
    "monitor_config": "/home/<USER>/git/cross_selction_strategy/fix_md_tools/config/fix_md_tool_config.json"
  }
}
```

### 8.4 监控指标
- **系统状态**: 当前状态、状态变化次数、状态持续时间
- **进程健康**: 心跳状态、重启次数、启动成功率
- **数据质量**: 补全次数、补全耗时、API成功率
- **性能指标**: 数据延迟、处理吞吐量、内存使用

## 9. 整体流程总结

### 9.1 正常运行流程
```
recorder → 写入数据 → 更新update_time → reader基于update_time读取
    ↓
monitor → 检查数据完整性 → 正常则继续监控
    ↓
python_scheduler → 监控进程心跳 → 正常则继续监控
```

### 9.2 异常处理流程
```
monitor检测缺失 → 尝试智能补全 → 区分三种结果：
    ├── 成功补全 → 替换共享内存 → 恢复正常
    ├── 合约过期 → 通知recorder剔除 → 恢复正常
    └── 补全失败 → 记录日志 → 恢复正常（不阻塞系统）

python_scheduler → 检测到DETECTING状态 → 杀死recorder → 等待补全完成 → 重启recorder
```

### 9.3 Reader优化流程
```
reader每次读取 → 检查update_time → 有更新才读取 → 更新缓存
    ↓
每分钟检查一次系统状态 → 检测共享内存文件变化 → 需要时重新映射
    ↓
异常状态时使用缓存数据 → 保证业务连续性
```

## 10. 优势和特点

1. **高性能**: Reader基于update_time读取，避免无效操作
2. **智能补全**: 区分可补全和过期合约，不阻塞系统运行
3. **业务连续性**: 补全期间Reader继续使用缓存数据
4. **合约生命周期管理**: 自动处理合约过期和剔除
5. **非阻塞设计**: 补全失败不影响系统正常运行
6. **状态管理清晰**: 明确的状态转换和责任分工
7. **进程管理灵活**: Python统一管理，支持多种控制策略
8. **异常处理完善**: 多层超时和恢复机制
9. **运维友好**: 丰富的监控指标和日志信息
10. **扩展性强**: 易于添加新功能和优化策略

## 11. 关键改进点

1. **解决了Reader性能问题**: 从频繁状态检查改为基于数据更新时间的智能读取
2. **处理了合约过期问题**: 智能识别过期合约并自动剔除，不影响系统运行
3. **优化了补全策略**: 部分补全、非阻塞设计，确保系统可用性
4. **提升了系统稳定性**: 补全失败不进入ERROR状态，允许系统继续运行
5. **增强了业务连续性**: Reader在任何情况下都能提供数据（实时或缓存）
