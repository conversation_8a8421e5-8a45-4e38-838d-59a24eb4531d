#include "data_integrity_checker.h"
#include "cpp_frame/utils/date.h"
#include <algorithm>
#include <cstring>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cerrno>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cerrno>

namespace fast_trader_elite {
namespace strategy {

data_integrity_checker::data_integrity_checker(kline_data_reader &reader,
                                               logger &logger)
    : reader_(reader), logger_(logger) {
  STRA_LOG(logger_, STRA_INFO, "初始化数据完整性检查器");

  // 从原始共享内存中读取配置值
  if (reader_.get_shm_ptr()) {
    max_klines_per_contract_ = reader_.get_shm_ptr()->header.max_klines_per_contract;
    STRA_LOG(logger_, STRA_INFO, "从原始共享内存读取配置: max_klines_per_contract = {}",
             max_klines_per_contract_);
  } else {
    STRA_LOG(logger_, STRA_ERROR, "无法从原始共享内存读取配置，使用默认值");
    max_klines_per_contract_ = 1200; // 使用默认值
  }

  // 复制数据到修复器
  fixed_data_ = reader_.get_all_kline_data();
}

data_integrity_checker::~data_integrity_checker() { cleanup_output_shm(); }

bool data_integrity_checker::check_data_integrity() {
  if (fixed_data_.empty()) {
    STRA_LOG(logger_, STRA_ERROR, "数据为空");
    return false;
  }

  // 处理重复数据
  process_duplicate_data();
  // 检查缺失数据
  check_missing_data();
  // 打印统计信息
  print_statistics();
  return true;
}

void data_integrity_checker::process_duplicate_data() {
  STRA_LOG(logger_, STRA_INFO, "开始处理重复数据");

  for (auto &pair : fixed_data_) {
    const std::string &instrument = pair.first;
    std::vector<kline_data_item> &klines = pair.second;

    if (klines.empty()) {
      continue;
    }
    std::sort(klines.begin(), klines.end());
    auto it = std::unique(klines.begin(), klines.end());
    size_t original_size = klines.size();
    size_t unique_count = std::distance(klines.begin(), it);

    if (unique_count < original_size) {
      duplicate_counts_[instrument] = original_size - unique_count;
      klines.erase(it, klines.end());
      STRA_LOG(logger_, STRA_WARN, "发现合约 {} 的 {} 条重复数据，已移除",
               instrument, (original_size - unique_count));
    }
  }

  STRA_LOG(logger_, STRA_INFO, "重复数据处理完成");
}

void data_integrity_checker::check_missing_data() {
  STRA_LOG(logger_, STRA_INFO, "开始检查缺失数据");

  const uint64_t one_minute_ms = 60 * 1000; // 1分钟的毫秒数

  // 遍历所有合约的K线数据
  for (const auto &pair : fixed_data_) {
    const std::string &instrument = pair.first;
    const std::vector<kline_data_item> &klines = pair.second;

    if (klines.size() < 2) {
      continue; // 至少需要两个数据点才能检查间隔
    }

    // 检查数据间隔
    std::vector<std::pair<uint64_t, uint64_t>> &ranges =
        missing_ranges_[instrument];

    for (size_t i = 1; i < klines.size(); ++i) {
      uint64_t prev_time = klines[i - 1].timestamp;
      uint64_t curr_time = klines[i].timestamp;
      uint64_t time_diff = curr_time - prev_time;

      // 检查是否有缺失的数据（间隔大于1分钟）
      if (time_diff > one_minute_ms) {
        uint64_t missing_count = (time_diff / one_minute_ms) - 1;
        STRA_LOG(logger_, STRA_WARN,
                 "发现合约 {} 的K线数据存在间隔: {} -> {} (缺失 {} 条K线)",
                 instrument, cpp_frame::date::ms_ts_to_readable(prev_time),
                 cpp_frame::date::ms_ts_to_readable(curr_time), missing_count);

        // 记录缺失的时间范围
        uint64_t start_time = prev_time + one_minute_ms;
        uint64_t end_time = curr_time;
        ranges.push_back({start_time, end_time});
      }
    }
  }

  STRA_LOG(logger_, STRA_INFO, "缺失数据检查完成");
}

void data_integrity_checker::print_statistics() const {
  STRA_LOG(logger_, STRA_INFO, "===== 数据完整性检查统计信息 =====");

  // 打印重复数据统计
  STRA_LOG(logger_, STRA_INFO, "重复数据统计:");
  if (duplicate_counts_.empty()) {
    STRA_LOG(logger_, STRA_INFO, "  没有发现重复数据");
  } else {
    int total_duplicates = 0;
    for (const auto &pair : duplicate_counts_) {
      STRA_LOG(logger_, STRA_INFO, "  合约 {}: {} 条重复数据", pair.first,
               pair.second);
      total_duplicates += pair.second;
    }
    STRA_LOG(logger_, STRA_INFO, "  总计: {} 条重复数据", total_duplicates);
  }

  // 打印缺失数据统计
  STRA_LOG(logger_, STRA_INFO, "缺失数据统计:");
  if (missing_ranges_.empty()) {
    STRA_LOG(logger_, STRA_INFO, "  没有发现缺失数据");
  } else {
    int total_missing = 0;
    for (const auto &pair : missing_ranges_) {
      const std::string &instrument = pair.first;
      const auto &ranges = pair.second;

      int missing_count = 0;
      for (const auto &range : ranges) {
        uint64_t start_time = range.first;
        uint64_t end_time = range.second;
        uint64_t time_diff = end_time - start_time;
        int count = (time_diff / (60 * 1000)) + 1;
        missing_count += count;
      }

      STRA_LOG(logger_, STRA_INFO, "  合约 {}: {} 个缺失区间，约 {} 条缺失数据",
               instrument, ranges.size(), missing_count);
      total_missing += missing_count;
    }
    STRA_LOG(logger_, STRA_INFO, "  总计: 约 {} 条缺失数据", total_missing);
  }

  STRA_LOG(logger_, STRA_INFO, "===== 统计信息结束 =====");
}

bool data_integrity_checker::write_to_output_shm(
    const std::string &output_shm_path) {
  STRA_LOG(logger_, STRA_INFO, "开始将修复后的数据写到输出共享内存: {}",
           output_shm_path);

  // 创建输出共享内存
  if (!create_output_shm(output_shm_path)) {
    STRA_LOG(logger_, STRA_ERROR, "创建输出共享内存失败");
    return false;
  }

  // 首先清空所有合约的数据
  for (uint32_t i = 0; i < output_shm_ptr_->header.contract_count; ++i) {
    contract_metadata &metadata = output_shm_ptr_->contracts[i];
    metadata.kline_count.store(0, std::memory_order_relaxed);
    metadata.head_index.store(0, std::memory_order_relaxed);
    metadata.last_timestamp = 0;

    // 清空数据区域 - 使用柔性数组访问方式
    uint32_t data_start_offset = i * max_klines_per_contract_;
    memset(&output_shm_ptr_->data[data_start_offset], 0,
           sizeof(kline_data) * max_klines_per_contract_);
  }

  STRA_LOG(logger_, STRA_INFO, "已清空输出共享内存中的所有数据");

  // 遍历所有合约的K线数据，全量写入
  for (const auto &pair : fixed_data_) {
    const std::string &instrument = pair.first;
    const std::vector<kline_data_item> &klines = pair.second;

    if (klines.empty()) {
      continue;
    }

    // 获取合约索引
    auto it = output_contract_indices_.find(instrument);
    if (it == output_contract_indices_.end()) {
      STRA_LOG(logger_, STRA_ERROR, "找不到合约 {} 的索引", instrument);
      continue;
    }

    uint32_t contract_index = it->second;

    // 写入K线数据
    for (const auto &kline : klines) {
      uint32_t next_index =
          output_shm_ptr_->contracts[contract_index].kline_count.load(
              std::memory_order_relaxed);
      if (next_index >= max_klines_per_contract_) {
        STRA_LOG(logger_, STRA_ERROR, "合约 {} 的K线数据超过最大限制",
                 instrument);
        break;
      }

      // 写入数据 - 使用柔性数组访问方式
      uint32_t data_offset = contract_index * max_klines_per_contract_ + next_index;
      kline_data &shm_kline = output_shm_ptr_->data[data_offset];
      shm_kline.timestamp = kline.timestamp;
      shm_kline.open = kline.open;
      shm_kline.high = kline.high;
      shm_kline.low = kline.low;
      shm_kline.close = kline.close;
      shm_kline.volume = kline.volume;

      // 更新计数
      output_shm_ptr_->contracts[contract_index].kline_count.store(
          next_index + 1, std::memory_order_release);

      // 更新最后时间戳
      output_shm_ptr_->contracts[contract_index].last_timestamp =
          kline.timestamp + 60 * 1000;
    }

    STRA_LOG(logger_, STRA_INFO, "已将合约 {} 的 {} 条K线数据写到输出共享内存",
             instrument, klines.size());
  }

  output_shm_ptr_->header.last_update_timestamp = 0;
  STRA_LOG(logger_, STRA_INFO, "数据写到输出共享内存完成");
  return true;
}

bool data_integrity_checker::create_output_shm(
    const std::string &output_shm_path) {
  output_shm_path_ = output_shm_path;

  // 计算输出共享内存大小
  output_shm_size_ = sizeof(kline_shm_header) +
                     sizeof(contract_metadata) * MAX_CONTRACTS +
                     sizeof(kline_data) * MAX_CONTRACTS * max_klines_per_contract_;

  // 手动创建内存映射文件
  int shm_fd = open(output_shm_path.c_str(), O_RDWR | O_CREAT, 0666);
  if (shm_fd == -1) {
    STRA_LOG(logger_, STRA_ERROR, "打开输出共享内存文件失败: {}, error: {}",
             output_shm_path, strerror(errno));
    return false;
  }

  if (ftruncate(shm_fd, output_shm_size_) != 0) {
    STRA_LOG(logger_, STRA_ERROR, "设置输出共享内存文件大小失败: {}, size: {}, error: {}",
             output_shm_path, output_shm_size_, strerror(errno));
    close(shm_fd);
    return false;
  }

  fallocate(shm_fd, 0, 0, output_shm_size_);

  output_shm_ptr_ = (kline_shm*)mmap(0, output_shm_size_, PROT_READ | PROT_WRITE,
                                     MAP_SHARED | MAP_POPULATE, shm_fd, 0);
  close(shm_fd);

  if (output_shm_ptr_ == MAP_FAILED) {
    STRA_LOG(logger_, STRA_ERROR, "内存映射失败: {}, error: {}", output_shm_path, strerror(errno));
    return false;
  }

  // 初始化输出共享内存头部
  output_shm_ptr_->header.magic = KLINE_SHM_MAGIC;
  output_shm_ptr_->header.version = KLINE_SHM_VERSION;
  output_shm_ptr_->header.max_contracts = MAX_CONTRACTS;
  // 使用存储的max_klines_per_contract配置
  output_shm_ptr_->header.max_klines_per_contract = max_klines_per_contract_;
  output_shm_ptr_->header.contract_count = 0;
  output_shm_ptr_->header.last_update_timestamp = time(nullptr) * 1000;
  memset(output_shm_ptr_->header.reserved, 0,
         sizeof(output_shm_ptr_->header.reserved));

  // 复制原始共享内存的合约信息到输出共享内存
  const auto &original_contract_indices = reader_.get_contract_indices();
  uint32_t contract_index = 0;

  for (const auto &pair : original_contract_indices) {
    const std::string &instrument = pair.first;

    // 设置合约元数据
    contract_metadata &metadata = output_shm_ptr_->contracts[contract_index];
    strncpy(metadata.instrument_name, instrument.c_str(),
            sizeof(metadata.instrument_name) - 1);
    metadata.instrument_name[sizeof(metadata.instrument_name) - 1] = '\0';
    metadata.kline_count.store(0, std::memory_order_relaxed);
    metadata.head_index.store(0, std::memory_order_relaxed);
    metadata.last_timestamp = 0;
    memset(metadata.reserved, 0, sizeof(metadata.reserved));

    // 建立合约索引映射
    output_contract_indices_[instrument] = contract_index;

    STRA_LOG(logger_, STRA_DEBUG, "输出共享内存添加合约: {} -> {}", instrument,
             contract_index);
    contract_index++;
  }

  output_shm_ptr_->header.contract_count = contract_index;

  STRA_LOG(logger_, STRA_INFO, "输出共享内存创建成功，合约数量: {}",
           contract_index);
  return true;
}

void data_integrity_checker::cleanup_output_shm() {
  if (output_shm_ptr_) {
    // 使用存储的共享内存大小释放内存
    if (munmap(output_shm_ptr_, output_shm_size_) != 0) {
      STRA_LOG(logger_, STRA_ERROR, "释放输出共享内存失败: {}", strerror(errno));
    }
    output_shm_ptr_ = nullptr;
  }
  output_contract_indices_.clear();
  output_shm_path_.clear();
}

} // namespace strategy
} // namespace fast_trader_elite
