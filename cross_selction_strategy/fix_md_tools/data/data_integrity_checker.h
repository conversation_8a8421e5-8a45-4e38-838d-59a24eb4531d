#pragma once

#include "kline_data_reader.h"
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

namespace fast_trader_elite {
namespace strategy {

// 数据完整性检查器类
class data_integrity_checker {
public:
  data_integrity_checker(kline_data_reader &reader, logger &logger);
  ~data_integrity_checker();

  // 检查数据完整性
  bool check_data_integrity();

  // 处理重复数据
  void process_duplicate_data();

  // 检查缺失数据
  void check_missing_data();

  // 将修复后的数据写到新的共享内存
  bool write_to_output_shm(const std::string &output_shm_path);

  // 获取缺失数据范围
  const std::unordered_map<std::string,
                           std::vector<std::pair<uint64_t, uint64_t>>> &
  get_missing_ranges() const {
    return missing_ranges_;
  }

  // 获取重复数据统计
  const std::unordered_map<std::string, int> &get_duplicate_counts() const {
    return duplicate_counts_;
  }

  // 获取修复后的数据
  const std::unordered_map<std::string, std::vector<kline_data_item>> &
  get_fixed_data() const {
    return fixed_data_;
  }

  // 更新修复后的数据
  void update_fixed_data(
      const std::unordered_map<std::string, std::vector<kline_data_item>>
          &new_data) {
    fixed_data_ = new_data;
  }

  // 合并新数据
  void merge_data(const std::string &instrument,
                  const std::vector<kline_data_item> &new_klines) {
    if (new_klines.empty()) {
      return;
    }

    // 将新数据添加到现有数据中
    if (fixed_data_.count(instrument) == 0) {
      fixed_data_[instrument] = new_klines;
    } else {
      fixed_data_[instrument].insert(fixed_data_[instrument].end(),
                                     new_klines.begin(), new_klines.end());
    }

    // 按时间戳排序
    std::sort(fixed_data_[instrument].begin(), fixed_data_[instrument].end());

    // 去除重复数据
    auto it = std::unique(fixed_data_[instrument].begin(),
                          fixed_data_[instrument].end());
    fixed_data_[instrument].erase(it, fixed_data_[instrument].end());
  }

  // 打印统计信息
  void print_statistics() const;

private:
  // K线数据读取器引用
  kline_data_reader &reader_;
  logger &logger_;

  // 修复后的数据
  std::unordered_map<std::string, std::vector<kline_data_item>> fixed_data_;

  // 缺失数据范围
  std::unordered_map<std::string, std::vector<std::pair<uint64_t, uint64_t>>>
      missing_ranges_;

  // 重复数据统计
  std::unordered_map<std::string, int> duplicate_counts_;

  // 输出共享内存相关
  kline_shm *output_shm_ptr_{nullptr};
  std::string output_shm_path_;
  std::unordered_map<std::string, int> output_contract_indices_;
  size_t output_shm_size_{0};  // 输出共享内存大小

  // 从原始共享内存中读取的配置值
  uint32_t max_klines_per_contract_{0};

  // 私有方法
  bool create_output_shm(const std::string &output_shm_path);
  void cleanup_output_shm();
};

} // namespace strategy
} // namespace fast_trader_elite
