#pragma once

#include "../../frame/strategy_logger.h"
#include "shm_kline_manager.h"
#include <string>
#include <unordered_map>
#include <vector>

namespace fast_trader_elite {
namespace strategy {

// K线数据结构
struct kline_data_item {
  uint64_t timestamp; // K线时间戳
  double open;        // 开盘价
  double high;        // 最高价
  double low;         // 最低价
  double close;       // 收盘价
  double volume;      // 成交量

  // 用于排序和去重
  bool operator<(const kline_data_item &other) const {
    return timestamp < other.timestamp;
  }

  bool operator==(const kline_data_item &other) const {
    return timestamp == other.timestamp;
  }
};

// K线数据读取器类
class kline_data_reader {
public:
  // 构造函数
  kline_data_reader(const std::string &shm_path, logger &logger);

  // 析构函数
  ~kline_data_reader();

  // 初始化
  bool init();

  // 读取所有K线数据
  bool read_all_data();

  // 获取所有K线数据
  const std::unordered_map<std::string, std::vector<kline_data_item>> &
  get_all_kline_data() const {
    return all_kline_data_;
  }

  // 获取共享内存指针
  kline_shm *get_shm_ptr() const { return shm_ptr_; }

  // 获取合约索引映射
  const std::unordered_map<std::string, int> &get_contract_indices() const {
    return contract_indices_;
  }

  // 获取日志器
  logger &get_logger() const { return logger_; }

private:
  // 共享内存路径
  std::string shm_path_;

  // 日志器
  logger &logger_;

  // 共享内存指针
  kline_shm *shm_ptr_{nullptr};

  // 共享内存大小
  size_t shm_size_{0};

  // 合约索引映射
  std::unordered_map<std::string, int> contract_indices_;

  // 所有合约的K线数据
  std::unordered_map<std::string, std::vector<kline_data_item>> all_kline_data_;

  // 是否已初始化
  bool initialized_{false};

  // 从共享内存中读取的配置值
  uint32_t max_klines_per_contract_{0};
};

} // namespace strategy
} // namespace fast_trader_elite
