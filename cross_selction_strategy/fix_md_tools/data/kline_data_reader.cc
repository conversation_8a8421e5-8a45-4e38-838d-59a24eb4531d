#include "kline_data_reader.h"
#include <cpp_frame/shm/shm_util.h>
#include <filesystem>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cerrno>
#include <cstring>

namespace fast_trader_elite {
namespace strategy {

kline_data_reader::kline_data_reader(const std::string &shm_path,
                                     logger &logger)
    : shm_path_(shm_path), logger_(logger), shm_ptr_(nullptr),
      initialized_(false) {
  STRA_LOG(logger_, STRA_INFO, "初始化K线数据读取器，共享内存路径: {}",
           shm_path_);
}

kline_data_reader::~kline_data_reader() {
  if (shm_ptr_) {
    // 使用存储的共享内存大小释放内存
    if (munmap(shm_ptr_, shm_size_) != 0) {
      // 记录错误，但不抛出异常（析构函数中）
      std::cerr << "释放共享内存失败: " << strerror(errno) << std::endl;
    }
    shm_ptr_ = nullptr;
  }
}

bool kline_data_reader::init() {
  // 检查共享内存文件是否存在
  if (!std::filesystem::exists(shm_path_)) {
    STRA_LOG(logger_, STRA_ERROR, "共享内存文件不存在: {}", shm_path_);
    return false;
  }

  // 首先打开文件获取文件大小
  int shm_fd = open(shm_path_.c_str(), O_RDONLY);
  if (shm_fd == -1) {
    STRA_LOG(logger_, STRA_ERROR, "打开共享内存文件失败: {}, error: {}", shm_path_, strerror(errno));
    return false;
  }

  // 获取文件大小
  struct stat file_stat;
  if (fstat(shm_fd, &file_stat) == -1) {
    STRA_LOG(logger_, STRA_ERROR, "获取共享内存文件大小失败: {}, error: {}", shm_path_, strerror(errno));
    close(shm_fd);
    return false;
  }

  size_t file_size = file_stat.st_size;
  STRA_LOG(logger_, STRA_INFO, "共享内存文件大小: {} bytes", file_size);

  // 映射共享内存
  shm_ptr_ = (kline_shm*)mmap(0, file_size, PROT_READ, MAP_SHARED, shm_fd, 0);
  close(shm_fd);

  if (shm_ptr_ == MAP_FAILED) {
    STRA_LOG(logger_, STRA_ERROR, "内存映射失败: {}, error: {}", shm_path_, strerror(errno));
    return false;
  }

  // 存储文件大小用于后续释放
  shm_size_ = file_size;

  // 验证共享内存头部
  if (shm_ptr_->header.magic != KLINE_SHM_MAGIC) {
    STRA_LOG(logger_, STRA_ERROR, "共享内存魔数不匹配，期望: {}, 实际: {}",
             KLINE_SHM_MAGIC, shm_ptr_->header.magic);
    return false;
  }

  // 存储配置值
  max_klines_per_contract_ = shm_ptr_->header.max_klines_per_contract;
  STRA_LOG(logger_, STRA_INFO, "读取共享内存配置: max_klines_per_contract = {}",
           max_klines_per_contract_);

  // 加载合约索引
  for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
    std::string instrument(shm_ptr_->contracts[i].instrument_name);
    contract_indices_[instrument] = i;
    STRA_LOG(logger_, STRA_DEBUG, "加载合约索引: {} -> {}", instrument, i);
  }

  STRA_LOG(logger_, STRA_INFO, "共享内存初始化成功，合约数量: {}",
           shm_ptr_->header.contract_count);
  initialized_ = true;
  return true;
}

bool kline_data_reader::read_all_data() {
  if (!initialized_ || !shm_ptr_) {
    STRA_LOG(logger_, STRA_ERROR, "共享内存未初始化");
    return false;
  }

  // 清空现有数据
  all_kline_data_.clear();

  // 读取所有合约的K线数据
  for (const auto &pair : contract_indices_) {
    const std::string &instrument = pair.first;
    uint32_t contract_index = pair.second;

    // 获取合约元数据
    const contract_metadata &metadata = shm_ptr_->contracts[contract_index];
    uint32_t kline_count = metadata.kline_count.load(std::memory_order_relaxed);
    uint32_t head_index = metadata.head_index.load(std::memory_order_relaxed);

    if (kline_count == 0) {
      STRA_LOG(logger_, STRA_DEBUG, "合约 {} 没有K线数据", instrument);
      continue;
    }

    // 创建该合约的K线数据缓存
    std::vector<kline_data_item> &klines = all_kline_data_[instrument];
    klines.reserve(kline_count);

    // 读取所有K线数据
    for (uint32_t i = 0; i < kline_count; ++i) {
      uint32_t idx = (head_index + i) % shm_ptr_->header.max_klines_per_contract;
      // 使用柔性数组访问：data[contract_index * max_klines_per_contract + idx]
      uint32_t data_offset = contract_index * shm_ptr_->header.max_klines_per_contract + idx;
      const kline_data &kline = shm_ptr_->data[data_offset];

      kline_data_item data;
      data.timestamp = kline.timestamp;
      data.open = kline.open;
      data.high = kline.high;
      data.low = kline.low;
      data.close = kline.close;
      data.volume = kline.volume;

      klines.push_back(data);
    }

    STRA_LOG(logger_, STRA_INFO, "已读取合约 {} 的 {} 条K线数据", instrument,
             klines.size());
  }

  STRA_LOG(logger_, STRA_INFO, "所有合约的K线数据读取完成，共 {} 个合约",
           all_kline_data_.size());
  return true;
}

} // namespace strategy
} // namespace fast_trader_elite
